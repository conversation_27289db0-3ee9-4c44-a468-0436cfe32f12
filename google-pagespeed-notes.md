
First Contentful Paint
1.5 s
Largest Contentful Paint
7.1 s
Total Blocking Time
240 ms
Cumulative Layout Shift
0
Speed Index
8.0 s
Captured at Jun 5, 2025, 6:18 PM GMT+3
Emulated Moto G Power with Lighthouse 12.6.0
Single page session
Initial page load
Slow 4G throttling
Using HeadlessChromium 136.0.7103.92 with lr


Insights
Improve image delivery Est savings of 358 KiB
Reducing the download time of images can improve the perceived load time of the page and LCP. Learn more about optimizing image sizeLCPFCP
URL
Resource Size
Est Savings
ponyclub.gr 1st party
449.8 KiB	329.9 KiB
/images/hero-image.webp(www.ponyclub.gr)
178.9 KiB
133.0 KiB
Increasing the image compression factor could improve this image's download size.
100.1 KiB
This image file is larger than it needs to be (630x768) for its displayed dimensions (613x460). Use responsive images to reduce the image download size.
74.8 KiB
/_next/image?url=%2Fimages%2Fhero-image.webp&w=750&q=85(www.ponyclub.gr)
118.0 KiB
75.7 KiB
Increasing the image compression factor could improve this image's download size.
75.7 KiB
/_next/image?url=%2Fimages%2Fround2.jpg&w=384&q=75(www.ponyclub.gr)
40.0 KiB
33.3 KiB
Increasing the image compression factor could improve this image's download size.
27.8 KiB
This image file is larger than it needs to be (384x195) for its displayed dimensions (165x248). Use responsive images to reduce the image download size.
18.2 KiB
/_next/image?url=%2Fimages%2Fponyclub_logo.png&w=384&q=75(www.ponyclub.gr)
27.9 KiB
25.6 KiB
Increasing the image compression factor could improve this image's download size.
22.6 KiB
This image file is larger than it needs to be (384x84) for its displayed dimensions (184x76). Use responsive images to reduce the image download size.
15.7 KiB
/_next/image?url=%2Fimages%2Fround1.jpg&w=384&q=75(www.ponyclub.gr)
27.3 KiB
24.8 KiB
Increasing the image compression factor could improve this image's download size.
15.2 KiB
This image file is larger than it needs to be (384x194) for its displayed dimensions (165x95). Use responsive images to reduce the image download size.
21.5 KiB
/_next/image?url=%2Fimages%2Fround3.jpg&w=384&q=75(www.ponyclub.gr)
23.7 KiB
20.4 KiB
Increasing the image compression factor could improve this image's download size.
11.5 KiB
This image file is larger than it needs to be (384x195) for its displayed dimensions (165x124). Use responsive images to reduce the image download size.
17.3 KiB
/_next/image?url=%2Fimages%2FRafting….jpg&w=384&q=75(www.ponyclub.gr)
24.4 KiB
11.6 KiB
Increasing the image compression factor could improve this image's download size.
11.6 KiB
/_next/image?url=%2Fimages%2Fround2.jpg&w=160&q=75(www.ponyclub.gr)
9.6 KiB
5.5 KiB
Increasing the image compression factor could improve this image's download size.
5.5 KiB
Other Google APIs/SDKs utility 
28.8 KiB	28.5 KiB
/a-/ALV-UjXA4…=s120-c-rp-mo-br100(lh3.googleusercontent.com)
28.8 KiB
28.5 KiB
Using a modern image format (WebP, AVIF) or increasing the image compression could improve this image's download size.
26.4 KiB
This image file is larger than it needs to be (120x120) for its displayed dimensions (40x40). Use responsive images to reduce the image download size.
25.6 KiB
Render blocking requests
Requests are blocking the page's initial render, which may delay LCP. Deferring or inlining can move these network requests out of the critical path.LCPFCP
URL
Transfer Size
Est Savings
ponyclub.gr 1st party
116.0 KiB	1,860 ms
…css/713eaa5c2eadeb35.css(www.ponyclub.gr)
109.1 KiB
1,380 ms
…css/d515496b8f5b2e0a.css(www.ponyclub.gr)
6.8 KiB
480 ms
Use efficient cache lifetimes Est savings of 1,102 KiB
A long cache lifetime can speed up repeat visits to your page. Learn more.LCPFCP
Request
Cache TTL
Transfer Size
elfsightcdn.com
833 KiB
…widget/allInOneReviews.js(universe-static.elfsightcdn.com)
1h
833 KiB
Google Maps utility 
221 KiB
…api/js?key=AIzaSyB9j…(maps.googleapis.com)
30m
221 KiB
elfsight.com
33 KiB
/platform/platform.js(static.elfsight.com)
1h
33 KiB
Other Google APIs/SDKs utility 
14 KiB
/a-/ALV-UjXA4…=s120-c-rp-mo-br100(lh3.googleusercontent.com)
1d
12 KiB
/a/ACg8ocIpK…=s120-c-rp-mo-br100(lh3.googleusercontent.com)
1d
2 KiB
bokun.io
1 KiB
…flags/gb.png(widgets.bokun.io)
1h
1 KiB
Legacy JavaScript Est savings of 43 KiB
Polyfills and transforms enable legacy browsers to use new JavaScript features. However, many aren't necessary for modern browsers. Consider modifying your JavaScript build process to not transpile Baseline features, unless you know you must support legacy browsers. Learn why most sites can deploy ES6+ code without transpilingLCPFCP
URL
Wasted bytes
ponyclub.gr 1st party
43.0 KiB
…chunks/951-1643eeba56b2ddc8.js(www.ponyclub.gr)
43.0 KiB
…chunks/951-1643eeba56b2ddc8.js:1:22923(www.ponyclub.gr)
Array.prototype.at
…chunks/951-1643eeba56b2ddc8.js:1:22311(www.ponyclub.gr)
Array.prototype.flat
…chunks/951-1643eeba56b2ddc8.js:1:22424(www.ponyclub.gr)
Array.prototype.flatMap
…chunks/951-1643eeba56b2ddc8.js:1:22800(www.ponyclub.gr)
Object.fromEntries
…chunks/951-1643eeba56b2ddc8.js:1:23058(www.ponyclub.gr)
Object.hasOwn
…chunks/951-1643eeba56b2ddc8.js:1:22053(www.ponyclub.gr)
String.prototype.trimEnd
…chunks/951-1643eeba56b2ddc8.js:1:21968(www.ponyclub.gr)
String.prototype.trimStart
Forced reflow
Many APIs, typically reading layout geometry, force the rendering engine to pause script execution in order to calculate the style and layout. Learn more about forced reflow and its mitigations.
Top function call
Total reflow time
…widget/allInOneReviews.js:66:13035(universe-static.elfsightcdn.com)
114 ms
Source
Total reflow time
…%5Blocale%5D/error-322470fc50a527e8.js:1:485(www.ponyclub.gr)
33 ms
[unattributed]
23 ms
/BokunWidgets.869f943….js:211:7535(static.bokun.io)
0 ms
…widget/allInOneReviews.js:41:75069(universe-static.elfsightcdn.com)
51 ms
…widget/allInOneReviews.js:41:75805(universe-static.elfsightcdn.com)
29 ms
…widget/allInOneReviews.js:91:201831(universe-static.elfsightcdn.com)
1 ms
…widget/allInOneReviews.js:65:60019(universe-static.elfsightcdn.com)
12 ms
…widget/allInOneReviews.js:65:71551(universe-static.elfsightcdn.com)
47 ms
…widget/allInOneReviews.js:65:77270(universe-static.elfsightcdn.com)
3 ms
…widget/allInOneReviews.js:65:77088(universe-static.elfsightcdn.com)
4 ms
/BokunWidgets.869f943….js:2:32099(static.bokun.io)
1 ms
LCP request discovery
Optimize LCP by making the LCP image discoverable from the HTML immediately, and avoiding lazy-loadingLCP
lazy load not applied
fetchpriority=high should be applied
Request is discoverable in initial document
main.relative > div.relative > div.absolute > video.absolute
<video src="/images/hero-video.mp4" poster="/images/hero-image.webp" autoplay="" loop="" playsinline="" preload="metadata" class="absolute inset-0 w-full h-full object-cover z-10">
Network dependency tree
Avoid chaining critical requests by reducing the length of chains, reducing the download size of resources, or deferring the download of unnecessary resources to improve page load.LCP
Maximum critical path latency: 535 ms
Initial Navigation
/en(www.ponyclub.gr) - 322 ms, 34.01 KiB
…css/713eaa5c2eadeb35.css(www.ponyclub.gr) - 535 ms, 109.11 KiB
…css/d515496b8f5b2e0a.css(www.ponyclub.gr) - 488 ms, 6.84 KiB
LCP by phase
Each phase has specific improvement strategies. Ideally, most of the LCP time should be spent on loading the resources, not within delays.LCP
Phase
Duration
Time to first byte
0 ms
Resource load delay
670 ms
Resource load duration
270 ms
Element render delay
180 ms
main.relative > div.relative > div.absolute > video.absolute
<video src="/images/hero-video.mp4" poster="/images/hero-image.webp" autoplay="" loop="" playsinline="" preload="metadata" class="absolute inset-0 w-full h-full object-cover z-10">
3rd parties
These insights are also available in the Chrome DevTools Performance Panel - record a trace to view more detailed information.
Diagnostics
Reduce JavaScript execution time 1.6 s
Consider reducing the time spent parsing, compiling, and executing JS. You may find delivering smaller JS payloads helps with this. Learn how to reduce Javascript execution time.TBT
URL
Total CPU Time
Script Evaluation
Script Parse
elfsightcdn.com
1,430 ms	967 ms	46 ms
…widget/allInOneReviews.js(universe-static.elfsightcdn.com)
1,430 ms
967 ms
46 ms
ponyclub.gr 1st party
665 ms	231 ms	16 ms
/en(www.ponyclub.gr)
299 ms
2 ms
3 ms
…chunks/951-1643eeba56b2ddc8.js(www.ponyclub.gr)
219 ms
164 ms
6 ms
…chunks/09f5f10c-b71eca879…f.js(www.ponyclub.gr)
147 ms
65 ms
7 ms
Google Tag Manager tag-manager 
220 ms	150 ms	19 ms
/gtag/js?id=G-6J3ELVNTQE(www.googletagmanager.com)
220 ms
150 ms
19 ms
bokun.io
195 ms	172 ms	17 ms
/BokunWidgets.869f943….js(static.bokun.io)
195 ms
172 ms
17 ms
Unattributable
127 ms	7 ms	0 ms
Unattributable
127 ms
7 ms
0 ms
Minimize main-thread work 2.7 s
Consider reducing the time spent parsing, compiling and executing JS. You may find delivering smaller JS payloads helps with this. Learn how to minimize main-thread workTBT
Category
Time Spent
Script Evaluation
1,576 ms
Style & Layout
438 ms
Other
385 ms
Garbage Collection
121 ms
Script Parsing & Compilation
115 ms
Rendering
68 ms
Parse HTML & CSS
10 ms
Reduce unused JavaScript Est savings of 486 KiB
Reduce unused JavaScript and defer loading scripts until they are required to decrease bytes consumed by network activity. Learn how to reduce unused JavaScript.LCPFCP
React
If you are not server-side rendering, split your JavaScript bundles with React.lazy(). Otherwise, code-split using a third-party library such as loadable-components.
URL
Transfer Size
Est Savings
elfsightcdn.com
1,017.7 KiB	328.9 KiB
…widget/allInOneReviews.js(universe-static.elfsightcdn.com)
1,017.7 KiB
328.9 KiB
bokun.io
138.8 KiB	70.7 KiB
/BokunWidgets.869f943….js(static.bokun.io)
138.8 KiB
70.7 KiB
Google Tag Manager tag-manager 
145.9 KiB	49.7 KiB
/gtag/js?id=G-6J3ELVNTQE(www.googletagmanager.com)
145.9 KiB
49.7 KiB
ponyclub.gr 1st party
42.4 KiB	36.5 KiB
…chunks/967-5fed4a969c53b60b.js(www.ponyclub.gr)
42.4 KiB
36.5 KiB
Avoid serving legacy JavaScript to modern browsers Est savings of 12 KiB
Polyfills and transforms enable legacy browsers to use new JavaScript features. However, many aren't necessary for modern browsers. Consider modifying your JavaScript build process to not transpile Baseline features, unless you know you must support legacy browsers. Learn why most sites can deploy ES6+ code without transpilingLCPFCP
URL
Est Savings
ponyclub.gr 1st party
11.3 KiB
…chunks/951-1643eeba56b2ddc8.js(www.ponyclub.gr)
11.3 KiB
…chunks/951-1643eeba56b2ddc8.js:1:22923(www.ponyclub.gr)
Array.prototype.at
…chunks/951-1643eeba56b2ddc8.js:1:22311(www.ponyclub.gr)
Array.prototype.flat
…chunks/951-1643eeba56b2ddc8.js:1:22424(www.ponyclub.gr)
Array.prototype.flatMap
…chunks/951-1643eeba56b2ddc8.js:1:22800(www.ponyclub.gr)
Object.fromEntries
…chunks/951-1643eeba56b2ddc8.js:1:23058(www.ponyclub.gr)
Object.hasOwn
…chunks/951-1643eeba56b2ddc8.js:1:22053(www.ponyclub.gr)
String.prototype.trimEnd
…chunks/951-1643eeba56b2ddc8.js:1:21968(www.ponyclub.gr)
String.prototype.trimStart
elfsightcdn.com
1.0 KiB
…widget/allInOneReviews.js(universe-static.elfsightcdn.com)
1.0 KiB
…widget/allInOneReviews.js:69:147915(universe-static.elfsightcdn.com)
@babel/plugin-transform-classes
Avoid enormous network payloads Total size was 5,177 KiB
Large network payloads cost users real money and are highly correlated with long load times. Learn how to reduce payload sizes.
URL
Transfer Size
ponyclub.gr 1st party
2,231.2 KiB
/images/hero-video.mp4(www.ponyclub.gr)
1,932.9 KiB
/images/hero-image.webp(www.ponyclub.gr)
179.6 KiB
/_next/image?url=%2Fimages%2Fhero-image.webp&w=750&q=85(www.ponyclub.gr)
118.7 KiB
elfsightcdn.com
1,041.7 KiB
…widget/allInOneReviews.js(universe-static.elfsightcdn.com)
1,041.7 KiB
bokun.io
353.0 KiB
/BokunWidgets.869f943….js(static.bokun.io)
139.4 KiB
/OnlineSal….a6aa804….js(static.bokun.io)
86.4 KiB
/46929.fb94355….js(static.bokun.io)
72.1 KiB
/OnlineSalesContent.e315bd9….js(static.bokun.io)
55.0 KiB
Google Tag Manager tag-manager 
147.0 KiB
/gtag/js?id=G-6J3ELVNTQE(www.googletagmanager.com)
147.0 KiB
Google Maps utility 
86.7 KiB
…api/js?key=AIzaSyB9j…(maps.googleapis.com)
86.7 KiB
Avoid long main-thread tasks 9 long tasks found
More information about the performance of your application. These numbers don't directly affect the Performance score.